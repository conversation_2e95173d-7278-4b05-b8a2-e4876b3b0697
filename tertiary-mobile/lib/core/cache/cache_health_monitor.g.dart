// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'cache_health_monitor.dart';

// **************************************************************************
// RiverpodGenerator
// **************************************************************************

String _$cacheHealthMonitorHash() =>
    r'63cd5db5082f9fbd039e45d6fb072604d46e6629';

/// See also [cacheHealthMonitor].
@ProviderFor(cacheHealthMonitor)
final cacheHealthMonitorProvider =
    AutoDisposeProvider<CacheHealthMonitor>.internal(
  cacheHealthMonitor,
  name: r'cacheHealthMonitorProvider',
  debugGetCreateSourceHash: const bool.fromEnvironment('dart.vm.product')
      ? null
      : _$cacheHealthMonitorHash,
  dependencies: null,
  allTransitiveDependencies: null,
);

@Deprecated('Will be removed in 3.0. Use Ref instead')
// ignore: unused_element
typedef CacheHealthMonitorRef = AutoDisposeProviderRef<CacheHealthMonitor>;
// ignore_for_file: type=lint
// ignore_for_file: subtype_of_sealed_class, invalid_use_of_internal_member, invalid_use_of_visible_for_testing_member, deprecated_member_use_from_same_package
