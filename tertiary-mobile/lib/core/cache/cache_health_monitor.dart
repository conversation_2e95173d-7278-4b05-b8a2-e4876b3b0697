import 'package:hooks_riverpod/hooks_riverpod.dart';
import 'package:riverpod_annotation/riverpod_annotation.dart';
import 'package:tertiary_mobile/core/cache/cache_manager.dart';
import 'package:tertiary_mobile/core/utils/logger.dart';

part 'cache_health_monitor.g.dart';

@riverpod
CacheHealthMonitor cacheHealthMonitor(Ref ref) {
  final cacheManager = ref.read(cacheManagerProvider);
  return CacheHealthMonitor(cacheManager);
}

/// Monitors cache health and provides utilities for cache management
class CacheHealthMonitor {
  final CacheManager _cacheManager;

  CacheHealthMonitor(this._cacheManager);

  /// Get comprehensive cache health information
  Future<CacheHealthInfo> getCacheHealth() async {
    try {
      final healthData = await _cacheManager.getCacheHealth();
      
      return CacheHealthInfo(
        totalCaches: healthData['totalCaches'] ?? 0,
        expiredCaches: healthData['expiredCaches'] ?? 0,
        totalRecords: healthData['totalRecords'] ?? 0,
        healthScore: healthData['healthScore'] ?? 0.0,
        caches: Map<String, CacheInfo>.from(
          (healthData['caches'] as Map<String, dynamic>? ?? {}).map(
            (key, value) => MapEntry(
              key,
              CacheInfo.fromMap(value as Map<String, dynamic>),
            ),
          ),
        ),
        error: healthData['error'] as String?,
      );
    } catch (e) {
      logger.e('Error getting cache health: $e');
      return CacheHealthInfo.error(e.toString());
    }
  }

  /// Clear all expired caches
  Future<void> clearExpiredCaches() async {
    try {
      await _cacheManager.clearExpiredCaches();
      logger.i('Successfully cleared expired caches');
    } catch (e) {
      logger.e('Error clearing expired caches: $e');
      rethrow;
    }
  }

  /// Get cache status for a specific cache key
  Future<CacheStatus> getCacheStatus(String cacheKey) async {
    try {
      final metadata = await _cacheManager.getCacheMetadata(cacheKey);
      if (metadata == null) {
        return CacheStatus.notFound;
      }

      if (metadata.isExpired) {
        return CacheStatus.expired;
      }

      if (metadata.needsRefresh()) {
        return CacheStatus.stale;
      }

      return CacheStatus.fresh;
    } catch (e) {
      logger.e('Error getting cache status for $cacheKey: $e');
      return CacheStatus.error;
    }
  }

  /// Log cache health summary
  Future<void> logCacheHealthSummary() async {
    try {
      final health = await getCacheHealth();
      
      logger.i('=== Cache Health Summary ===');
      logger.i('Total Caches: ${health.totalCaches}');
      logger.i('Expired Caches: ${health.expiredCaches}');
      logger.i('Total Records: ${health.totalRecords}');
      logger.i('Health Score: ${(health.healthScore * 100).toStringAsFixed(1)}%');
      
      if (health.caches.isNotEmpty) {
        logger.i('Cache Details:');
        health.caches.forEach((key, info) {
          final status = info.isExpired ? 'EXPIRED' : 'OK';
          logger.i('  $key: ${info.recordCount} records [$status]');
        });
      }
      
      if (health.error != null) {
        logger.w('Cache Health Error: ${health.error}');
      }
      
      logger.i('============================');
    } catch (e) {
      logger.e('Error logging cache health summary: $e');
    }
  }
}

/// Cache health information
class CacheHealthInfo {
  final int totalCaches;
  final int expiredCaches;
  final int totalRecords;
  final double healthScore;
  final Map<String, CacheInfo> caches;
  final String? error;

  const CacheHealthInfo({
    required this.totalCaches,
    required this.expiredCaches,
    required this.totalRecords,
    required this.healthScore,
    required this.caches,
    this.error,
  });

  factory CacheHealthInfo.error(String error) {
    return CacheHealthInfo(
      totalCaches: 0,
      expiredCaches: 0,
      totalRecords: 0,
      healthScore: 0.0,
      caches: {},
      error: error,
    );
  }

  bool get hasError => error != null;
  bool get isHealthy => healthScore > 0.8;
  int get healthyCaches => totalCaches - expiredCaches;
}

/// Individual cache information
class CacheInfo {
  final int recordCount;
  final String lastUpdated;
  final String lastFetched;
  final bool isExpired;
  final String policy;
  final String? version;

  const CacheInfo({
    required this.recordCount,
    required this.lastUpdated,
    required this.lastFetched,
    required this.isExpired,
    required this.policy,
    this.version,
  });

  factory CacheInfo.fromMap(Map<String, dynamic> map) {
    return CacheInfo(
      recordCount: map['recordCount'] ?? 0,
      lastUpdated: map['lastUpdated'] ?? '',
      lastFetched: map['lastFetched'] ?? '',
      isExpired: map['isExpired'] ?? false,
      policy: map['policy'] ?? 'unknown',
      version: map['version'],
    );
  }
}

/// Cache status enumeration
enum CacheStatus {
  fresh,
  stale,
  expired,
  notFound,
  error,
}

extension CacheStatusExtension on CacheStatus {
  String get displayName {
    switch (this) {
      case CacheStatus.fresh:
        return 'Fresh';
      case CacheStatus.stale:
        return 'Stale';
      case CacheStatus.expired:
        return 'Expired';
      case CacheStatus.notFound:
        return 'Not Found';
      case CacheStatus.error:
        return 'Error';
    }
  }

  bool get isHealthy => this == CacheStatus.fresh || this == CacheStatus.stale;
}
