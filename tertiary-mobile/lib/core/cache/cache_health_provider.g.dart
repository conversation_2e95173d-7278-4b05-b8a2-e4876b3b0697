// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'cache_health_provider.dart';

// **************************************************************************
// RiverpodGenerator
// **************************************************************************

String _$cacheHealthScoreHash() => r'bed0e528f8f3467015487b62f014407343edfcce';

/// Convenience provider for cache health score
///
/// Copied from [cacheHealthScore].
@ProviderFor(cacheHealthScore)
final cacheHealthScoreProvider = AutoDisposeProvider<double>.internal(
  cacheHealthScore,
  name: r'cacheHealthScoreProvider',
  debugGetCreateSourceHash: const bool.fromEnvironment('dart.vm.product')
      ? null
      : _$cacheHealthScoreHash,
  dependencies: null,
  allTransitiveDependencies: null,
);

@Deprecated('Will be removed in 3.0. Use Ref instead')
// ignore: unused_element
typedef CacheHealthScoreRef = AutoDisposeProviderRef<double>;
String _$cacheNeedsAttentionHash() =>
    r'da546a89a346cc833db8c083457d04295a8408c4';

/// Convenience provider for checking if caches need attention
///
/// Copied from [cacheNeedsAttention].
@ProviderFor(cacheNeedsAttention)
final cacheNeedsAttentionProvider = AutoDisposeProvider<bool>.internal(
  cacheNeedsAttention,
  name: r'cacheNeedsAttentionProvider',
  debugGetCreateSourceHash: const bool.fromEnvironment('dart.vm.product')
      ? null
      : _$cacheNeedsAttentionHash,
  dependencies: null,
  allTransitiveDependencies: null,
);

@Deprecated('Will be removed in 3.0. Use Ref instead')
// ignore: unused_element
typedef CacheNeedsAttentionRef = AutoDisposeProviderRef<bool>;
String _$cacheHealthNotifierHash() =>
    r'ab6bd30b120d5068d0fee6a539c539c4f5af179f';

/// Provider for monitoring cache health and performance
///
/// Copied from [CacheHealthNotifier].
@ProviderFor(CacheHealthNotifier)
final cacheHealthNotifierProvider = AutoDisposeAsyncNotifierProvider<
    CacheHealthNotifier, Map<String, dynamic>>.internal(
  CacheHealthNotifier.new,
  name: r'cacheHealthNotifierProvider',
  debugGetCreateSourceHash: const bool.fromEnvironment('dart.vm.product')
      ? null
      : _$cacheHealthNotifierHash,
  dependencies: null,
  allTransitiveDependencies: null,
);

typedef _$CacheHealthNotifier = AutoDisposeAsyncNotifier<Map<String, dynamic>>;
// ignore_for_file: type=lint
// ignore_for_file: subtype_of_sealed_class, invalid_use_of_internal_member, invalid_use_of_visible_for_testing_member, deprecated_member_use_from_same_package
