import 'dart:async';
import 'package:hooks_riverpod/hooks_riverpod.dart';
import 'package:riverpod_annotation/riverpod_annotation.dart';
import 'package:tertiary_mobile/core/utils/logger.dart';

part 'optimistic_updates_manager.g.dart';

@riverpod
OptimisticUpdatesManager optimisticUpdatesManager(Ref ref) {
  return OptimisticUpdatesManager();
}

/// Manages optimistic updates with automatic rollback on failure
class OptimisticUpdatesManager {
  final Map<String, OptimisticOperation> _pendingOperations = {};
  final StreamController<OptimisticUpdateEvent> _eventController = 
      StreamController<OptimisticUpdateEvent>.broadcast();

  /// Stream of optimistic update events
  Stream<OptimisticUpdateEvent> get events => _eventController.stream;

  /// Execute an optimistic update
  Future<T> executeOptimisticUpdate<T>({
    required String operationId,
    required T Function() optimisticUpdate,
    required Future<T> Function() serverOperation,
    required void Function() rollback,
    Duration timeout = const Duration(seconds: 30),
    int maxRetries = 3,
  }) async {
    logger.d('Starting optimistic update: $operationId');
    
    // 1. Apply optimistic update immediately
    final optimisticResult = optimisticUpdate();
    _eventController.add(OptimisticUpdateEvent.started(operationId));
    
    // 2. Track the operation
    final operation = OptimisticOperation(
      id: operationId,
      rollback: rollback,
      startTime: DateTime.now(),
    );
    _pendingOperations[operationId] = operation;
    
    try {
      // 3. Execute server operation with timeout and retries
      final serverResult = await _executeWithRetry(
        serverOperation,
        maxRetries: maxRetries,
        timeout: timeout,
      );
      
      // 4. Success - remove from pending operations
      _pendingOperations.remove(operationId);
      _eventController.add(OptimisticUpdateEvent.succeeded(operationId));
      
      logger.d('Optimistic update succeeded: $operationId');
      return serverResult;
      
    } catch (error) {
      // 5. Failure - rollback optimistic changes
      logger.w('Optimistic update failed: $operationId - $error');
      
      try {
        rollback();
        _eventController.add(OptimisticUpdateEvent.failed(operationId, error));
      } catch (rollbackError) {
        logger.e('Rollback failed for $operationId: $rollbackError');
        _eventController.add(OptimisticUpdateEvent.rollbackFailed(
          operationId, 
          error, 
          rollbackError,
        ));
      }
      
      _pendingOperations.remove(operationId);
      rethrow;
    }
  }

  /// Execute operation with retry logic
  Future<T> _executeWithRetry<T>(
    Future<T> Function() operation, {
    required int maxRetries,
    required Duration timeout,
  }) async {
    int attempts = 0;
    
    while (attempts < maxRetries) {
      try {
        return await operation().timeout(timeout);
      } catch (error) {
        attempts++;
        if (attempts >= maxRetries) {
          rethrow;
        }
        
        // Exponential backoff
        final delay = Duration(milliseconds: 1000 * (1 << (attempts - 1)));
        logger.d('Retry attempt $attempts after ${delay.inMilliseconds}ms');
        await Future.delayed(delay);
      }
    }
    
    throw Exception('Max retries exceeded');
  }

  /// Check if an operation is pending
  bool isOperationPending(String operationId) {
    return _pendingOperations.containsKey(operationId);
  }

  /// Get all pending operations
  List<OptimisticOperation> getPendingOperations() {
    return _pendingOperations.values.toList();
  }

  /// Cancel a pending operation (rollback without server call)
  void cancelOperation(String operationId) {
    final operation = _pendingOperations[operationId];
    if (operation != null) {
      try {
        operation.rollback();
        _eventController.add(OptimisticUpdateEvent.cancelled(operationId));
        logger.d('Cancelled optimistic operation: $operationId');
      } catch (error) {
        logger.e('Failed to cancel operation $operationId: $error');
      }
      _pendingOperations.remove(operationId);
    }
  }

  /// Cancel all pending operations
  void cancelAllOperations() {
    final operationIds = _pendingOperations.keys.toList();
    for (final operationId in operationIds) {
      cancelOperation(operationId);
    }
    logger.i('Cancelled all pending optimistic operations');
  }

  /// Get operation status
  OptimisticOperationStatus getOperationStatus(String operationId) {
    if (_pendingOperations.containsKey(operationId)) {
      return OptimisticOperationStatus.pending;
    }
    return OptimisticOperationStatus.completed;
  }

  /// Clean up old completed operations from memory
  void cleanup() {
    final now = DateTime.now();
    final expiredOperations = _pendingOperations.entries
        .where((entry) => 
            now.difference(entry.value.startTime) > Duration(minutes: 5))
        .map((entry) => entry.key)
        .toList();
    
    for (final operationId in expiredOperations) {
      cancelOperation(operationId);
    }
    
    if (expiredOperations.isNotEmpty) {
      logger.d('Cleaned up ${expiredOperations.length} expired operations');
    }
  }

  /// Dispose resources
  void dispose() {
    cancelAllOperations();
    _eventController.close();
  }
}

/// Represents a pending optimistic operation
class OptimisticOperation {
  final String id;
  final void Function() rollback;
  final DateTime startTime;

  OptimisticOperation({
    required this.id,
    required this.rollback,
    required this.startTime,
  });

  Duration get duration => DateTime.now().difference(startTime);
}

/// Events emitted during optimistic updates
sealed class OptimisticUpdateEvent {
  final String operationId;
  final DateTime timestamp;

  OptimisticUpdateEvent(this.operationId) : timestamp = DateTime.now();

  factory OptimisticUpdateEvent.started(String operationId) = 
      OptimisticUpdateStarted;
  
  factory OptimisticUpdateEvent.succeeded(String operationId) = 
      OptimisticUpdateSucceeded;
  
  factory OptimisticUpdateEvent.failed(String operationId, dynamic error) = 
      OptimisticUpdateFailed;
  
  factory OptimisticUpdateEvent.cancelled(String operationId) = 
      OptimisticUpdateCancelled;
  
  factory OptimisticUpdateEvent.rollbackFailed(
    String operationId, 
    dynamic originalError, 
    dynamic rollbackError,
  ) = OptimisticUpdateRollbackFailed;
}

class OptimisticUpdateStarted extends OptimisticUpdateEvent {
  OptimisticUpdateStarted(super.operationId);
}

class OptimisticUpdateSucceeded extends OptimisticUpdateEvent {
  OptimisticUpdateSucceeded(super.operationId);
}

class OptimisticUpdateFailed extends OptimisticUpdateEvent {
  final dynamic error;
  OptimisticUpdateFailed(super.operationId, this.error);
}

class OptimisticUpdateCancelled extends OptimisticUpdateEvent {
  OptimisticUpdateCancelled(super.operationId);
}

class OptimisticUpdateRollbackFailed extends OptimisticUpdateEvent {
  final dynamic originalError;
  final dynamic rollbackError;
  
  OptimisticUpdateRollbackFailed(
    super.operationId, 
    this.originalError, 
    this.rollbackError,
  );
}

/// Status of an optimistic operation
enum OptimisticOperationStatus {
  pending,
  completed,
}

/// Extension methods for easier usage
extension OptimisticUpdatesManagerExtension on OptimisticUpdatesManager {
  /// Convenience method for simple optimistic updates
  Future<void> simpleOptimisticUpdate({
    required String operationId,
    required void Function() optimisticUpdate,
    required Future<void> Function() serverOperation,
    required void Function() rollback,
  }) async {
    await executeOptimisticUpdate<void>(
      operationId: operationId,
      optimisticUpdate: () {
        optimisticUpdate();
        return;
      },
      serverOperation: serverOperation,
      rollback: rollback,
    );
  }
}
