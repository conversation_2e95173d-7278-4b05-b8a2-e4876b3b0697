// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'optimistic_updates_manager.dart';

// **************************************************************************
// RiverpodGenerator
// **************************************************************************

String _$optimisticUpdatesManagerHash() =>
    r'3528cc4a7bfbac10c90812e3dd6f956a58dfb165';

/// See also [optimisticUpdatesManager].
@ProviderFor(optimisticUpdatesManager)
final optimisticUpdatesManagerProvider =
    AutoDisposeProvider<OptimisticUpdatesManager>.internal(
  optimisticUpdatesManager,
  name: r'optimisticUpdatesManagerProvider',
  debugGetCreateSourceHash: const bool.fromEnvironment('dart.vm.product')
      ? null
      : _$optimisticUpdatesManagerHash,
  dependencies: null,
  allTransitiveDependencies: null,
);

@Deprecated('Will be removed in 3.0. Use Ref instead')
// ignore: unused_element
typedef OptimisticUpdatesManagerRef
    = AutoDisposeProviderRef<OptimisticUpdatesManager>;
// ignore_for_file: type=lint
// ignore_for_file: subtype_of_sealed_class, invalid_use_of_internal_member, invalid_use_of_visible_for_testing_member, deprecated_member_use_from_same_package
