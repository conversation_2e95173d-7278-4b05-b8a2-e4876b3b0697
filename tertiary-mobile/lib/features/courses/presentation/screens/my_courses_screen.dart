import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:hooks_riverpod/hooks_riverpod.dart';
import 'package:tertiary_mobile/core/constants/colors.dart';
import 'package:tertiary_mobile/features/authentication/providers/auth_provider.dart';
import 'package:tertiary_mobile/features/courses/presentation/widgets/course_table.dart';
import 'package:tertiary_mobile/features/courses/presentation/widgets/course_table_skeleton.dart';
import 'package:tertiary_mobile/features/courses/providers/courses_provider.dart';
import 'package:tertiary_mobile/features/courses/providers/selected_session_provider.dart';
import 'package:tertiary_mobile/routing/router.dart';
import 'package:tertiary_mobile/shared/presentation/layout/main_app_custom_appbar.dart';
import 'package:tertiary_mobile/shared/presentation/widgets/session_dropdown.dart';
import 'package:tertiary_mobile/shared/providers/session_semester_provider.dart';

class MyCoursesScreen extends ConsumerWidget {
  const MyCoursesScreen({super.key});

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final colors = ref.watch(appColorsProvider);
    final router = ref.read(routerProvider);
    final selectedSession = ref.watch(selectedSessionProvider);
    final user = ref.watch(authProvider).maybeWhen(
          data: (user) => user,
          orElse: () => null,
        );

    final groupedCourses =
        ref.watch(groupedCourseRegistrationsProvider(selectedSession));

    return Scaffold(
      appBar: const CustomAppBar(title: 'My Courses'),
      body: Padding(
        padding: EdgeInsets.all(12.w),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text('Matric No:',
                        style: TextStyle(
                          color: colors.primary,
                          fontWeight: FontWeight.bold,
                        )),
                    Text(
                      user?.studentNumber ?? 'N/A',
                      style: TextStyle(
                        color: colors.primary,
                        fontWeight: FontWeight.w500,
                      ),
                    ),
                  ],
                ),
                SizedBox(width: 16.w),
                Expanded(
                  child: SessionDropdown(
                    selectedSessionId: selectedSession,
                    onChanged: (sessionId) {
                      ref
                          .read(selectedSessionProvider.notifier)
                          .setSession(sessionId);
                    },
                  ),
                ),
              ],
            ),
            SizedBox(height: 20.h),
            Expanded(
              child: groupedCourses.when(
                loading: () => const CourseTableSkeleton(
                  columns: ['code', 'title', 'credit', 'regStatus', 'options'],
                ),
                error: (err, stack) => RefreshIndicator(
                  onRefresh: () async {
                    await ref
                        .read(coursesNotifierProvider(selectedSession).notifier)
                        .refresh(selectedSession);
                  },
                  child: SingleChildScrollView(
                    physics: const AlwaysScrollableScrollPhysics(),
                    child: SizedBox(
                      height: MediaQuery.of(context).size.height * 0.6,
                      child: const Center(
                        child: Text(
                            'Failed to load registrations\nPull to refresh'),
                      ),
                    ),
                  ),
                ),
                data: (grouped) {
                  // Check if we're still in initial loading state (empty data but provider is still loading)
                  final isInitialLoading = grouped.isEmpty &&
                      ref
                          .read(coursesNotifierProvider(selectedSession))
                          .isLoading;

                  if (isInitialLoading) {
                    return const CourseTableSkeleton(
                      columns: [
                        'code',
                        'title',
                        'credit',
                        'regStatus',
                        'options'
                      ],
                    );
                  }

                  if (grouped.isEmpty) {
                    return RefreshIndicator(
                      onRefresh: () async {
                        await ref
                            .read(coursesNotifierProvider(selectedSession)
                                .notifier)
                            .refresh(selectedSession);
                      },
                      child: SingleChildScrollView(
                        physics: const AlwaysScrollableScrollPhysics(),
                        child: SizedBox(
                          height: MediaQuery.of(context).size.height * 0.6,
                          child: const Center(
                            child: Text(
                                'No registered courses for the selected Session\nPull to refresh'),
                          ),
                        ),
                      ),
                    );
                  }

                  final sessions =
                      ref.read(sessionSemesterNotifierProvider).maybeWhen(
                            data: (sessions) => sessions,
                            orElse: () => [],
                          );
                  final sessionName = sessions
                          .where((s) => s.id == selectedSession)
                          .map((s) => s.sessionName)
                          .firstOrNull ??
                      '';

                  return RefreshIndicator(
                    onRefresh: () async {
                      await ref
                          .read(
                              coursesNotifierProvider(selectedSession).notifier)
                          .refresh(selectedSession);
                    },
                    child: ListView(
                      physics: const AlwaysScrollableScrollPhysics(),
                      children: [
                        for (int i = 0; i < grouped.entries.length; i++) ...[
                          Column(
                            crossAxisAlignment: CrossAxisAlignment.start,
                            children: [
                              if (sessionName.isNotEmpty)
                                Padding(
                                  padding: EdgeInsets.only(bottom: 4.h),
                                  child: Text(
                                    sessionName,
                                    style: TextStyle(
                                      color: Colors.black,
                                      fontWeight: FontWeight.bold,
                                      fontSize: 14,
                                    ),
                                  ),
                                ),
                              Padding(
                                padding: EdgeInsets.only(bottom: 12.h),
                                child: Text(
                                  '${grouped.entries.elementAt(i).key} Courses',
                                  style: TextStyle(
                                    color: Colors.black,
                                    fontWeight: FontWeight.bold,
                                    fontSize: 13,
                                  ),
                                ),
                              ),
                              CourseTable(
                                items: grouped.entries.elementAt(i).value,
                                columns: const [
                                  'code',
                                  'title',
                                  'credit',
                                  'options'
                                ],
                                optionsBuilder: (context, reg) {
                                  return PopupMenuButton(
                                    icon: Icon(Icons.more_horiz,
                                        color: colors.primary),
                                    onSelected: (action) {
                                      if (action == 'info') {
                                        router.pushNamed('courseinfo',
                                            extra: reg);
                                      }
                                    },
                                    itemBuilder: (_) => [
                                      PopupMenuItem(
                                        value: 'info',
                                        child: const Text('Course Info'),
                                      ),
                                    ],
                                  );
                                },
                              ),
                            ],
                          ),
                          if (i != grouped.entries.length - 1)
                            SizedBox(height: 24.h),
                        ]
                      ],
                    ),
                  );
                },
              ),
            ),
            SizedBox(height: 8.h),
          ],
        ),
      ),
    );
  }
}
