import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:hooks_riverpod/hooks_riverpod.dart';
import 'package:go_router/go_router.dart';
import 'package:tertiary_mobile/core/constants/colors.dart';
import 'package:tertiary_mobile/features/courses/domain/course_table_item.dart';
import 'package:tertiary_mobile/features/courses/presentation/widgets/course_table.dart';
import 'package:tertiary_mobile/features/courses/providers/course_registration_provider.dart';
import 'package:tertiary_mobile/routing/router.dart';
import 'package:tertiary_mobile/shared/presentation/layout/main_app_custom_appbar.dart';

class CourseSummaryScreen extends ConsumerWidget {
  final List<CourseTableItem> selectedCourses;

  const CourseSummaryScreen({
    super.key,
    required this.selectedCourses,
  });

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final colors = ref.watch(appColorsProvider);
    final GoRouter router = ref.read(routerProvider);

    final registeredCourses = selectedCourses;
    final total = registeredCourses.fold(0, (s, c) => s + c.creditUnit);

    // Group courses by semester
    final Map<String, List<CourseTableItem>> groupedCourses = {};
    for (final course in registeredCourses) {
      final semesterKey = course.session?.semesters.first.title ??
          course.session?.semesters.first.accronym ??
          'Semester';
      groupedCourses.putIfAbsent(semesterKey, () => []).add(course);
    }

    return Scaffold(
      appBar: const CustomAppBar(title: 'Course Summary'),
      body: Padding(
        padding: EdgeInsets.all(12.w),
        child: SingleChildScrollView(
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              // Display tables grouped by semester
              for (var entry in groupedCourses.entries) ...[
                Padding(
                  padding: EdgeInsets.only(
                      bottom: 12.h,
                      top: entry.key == groupedCourses.keys.first ? 0 : 24.h),
                  child: Text(
                    '${entry.key} Courses',
                    style: TextStyle(
                      color: colors.primary,
                      fontWeight: FontWeight.bold,
                      fontSize: 16.sp,
                    ),
                  ),
                ),
                CourseTable(
                  key: ValueKey('summary_table_${entry.key}'),
                  items: entry.value,
                  onViewInfo: (c) => router.pushNamed('courseinfo', extra: c),
                ),
              ],
              SizedBox(height: 30.h),
              Text(
                'Total load Units: $total',
                style: TextStyle(
                  color: colors.primary,
                  fontWeight: FontWeight.bold,
                ),
              ),
              SizedBox(height: 20.h),
              Center(
                child: ElevatedButton(
                  onPressed: () => _showConfirmDialog(context, colors, router),
                  style: ElevatedButton.styleFrom(
                    backgroundColor: colors.primary,
                    shape: RoundedRectangleBorder(
                      borderRadius: BorderRadius.circular(8.r),
                    ),
                    padding: EdgeInsets.symmetric(
                      horizontal: 24.w,
                      vertical: 12.h,
                    ),
                  ),
                  child: Text(
                    'Submit Course Form',
                    style: TextStyle(color: Colors.white),
                  ),
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }

  void _showConfirmDialog(
    BuildContext context,
    AppColors colors,
    GoRouter router,
  ) {
    final sessionId = selectedCourses.firstOrNull?.session?.id;
    if (sessionId == null) {
      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(content: Text('No session found')),
      );
      return;
    }

    showDialog(
      context: context,
      builder: (context) {
        return Consumer(
          builder: (context, ref, _) {
            // Watch only the specific state properties we need
            final state =
                ref.watch(courseRegistrationNotifierProvider(sessionId).select(
              (state) => state.valueOrNull,
            ));
            final isSubmitting = state?.isSubmitting ?? false;
            final error = state?.submissionError;

            Widget dialogContent;

            if (isSubmitting) {
              dialogContent = Column(
                mainAxisSize: MainAxisSize.min,
                children: [
                  const CircularProgressIndicator(),
                  SizedBox(height: 16.h),
                  const Text('Submitting...'),
                ],
              );
            } else if (error != null) {
              dialogContent = Column(
                mainAxisSize: MainAxisSize.min,
                children: [
                  Icon(
                    Icons.error_outline,
                    size: 48.sp,
                    color: Colors.red,
                  ),
                  SizedBox(height: 16.h),
                  Text(
                    'Error: $error',
                    textAlign: TextAlign.center,
                    style: const TextStyle(color: Colors.red),
                  ),
                  SizedBox(height: 24.h),
                  SizedBox(
                    width: double.infinity,
                    child: ElevatedButton(
                      onPressed: () => Navigator.of(context).pop(),
                      child: const Text('OK'),
                    ),
                  ),
                ],
              );
            } else {
              dialogContent = Column(
                mainAxisSize: MainAxisSize.min,
                children: [
                  Icon(
                    Icons.warning_amber_rounded,
                    size: 48.sp,
                    color: colors.primary,
                  ),
                  SizedBox(height: 16.h),
                  const Text(
                    'Do you want to save the course registration form?',
                    textAlign: TextAlign.center,
                    style: TextStyle(fontWeight: FontWeight.w500),
                  ),
                  SizedBox(height: 24.h),
                  SizedBox(
                    width: double.infinity,
                    child: ElevatedButton(
                      onPressed: () async {
                        try {
                          final notifier = ref.read(
                            courseRegistrationNotifierProvider(sessionId)
                                .notifier,
                          );
                          await notifier.submitRegistration();
                          if (context.mounted) {
                            Navigator.of(context).pop();
                            Navigator.of(context).pop();
                          }
                        } catch (_) {
                          // Error is already handled by the state
                        }
                      },
                      style: ElevatedButton.styleFrom(
                        backgroundColor: colors.primary,
                        shape: RoundedRectangleBorder(
                          borderRadius: BorderRadius.circular(8.r),
                        ),
                        padding: EdgeInsets.symmetric(vertical: 10.h),
                      ),
                      child: const Text(
                        'Submit',
                        style: TextStyle(color: Colors.white),
                      ),
                    ),
                  ),
                  SizedBox(height: 8.h),
                  TextButton(
                    onPressed: () => Navigator.of(context).pop(),
                    child: Text(
                      'Cancel',
                      style: TextStyle(
                        color: colors.primary,
                        fontWeight: FontWeight.w500,
                      ),
                    ),
                  ),
                ],
              );
            }

            return Dialog(
              shape: RoundedRectangleBorder(
                borderRadius: BorderRadius.circular(12.r),
              ),
              insetPadding: EdgeInsets.symmetric(horizontal: 40.w),
              child: Padding(
                padding: EdgeInsets.all(24.w),
                child: dialogContent,
              ),
            );
          },
        );
      },
    );
  }
}
