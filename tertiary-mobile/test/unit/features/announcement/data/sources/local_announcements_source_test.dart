import 'package:drift/native.dart';
import 'package:flutter_test/flutter_test.dart';
import 'package:mocktail/mocktail.dart';
import 'package:tertiary_mobile/core/utils/local_database.dart';
import 'package:tertiary_mobile/core/cache/cache_manager.dart';
import 'package:tertiary_mobile/core/cache/cache_policy.dart';
import 'package:tertiary_mobile/features/announcement/data/models/announcement_model.dart';
import 'package:tertiary_mobile/features/announcement/data/sources/local_announcements_source.dart';

class MockCacheManager extends Mock implements CacheManager {}

void main() {
  late LocalDatabase db;
  late LocalAnnouncementsSource source;
  late MockCacheManager mockCacheManager;

  setUpAll(() {
    registerFallbackValue(CachePolicy.medium);
  });

  setUp(() {
    db = LocalDatabase(NativeDatabase.memory());
    mockCacheManager = MockCacheManager();

    // Set up mock behavior
    when(() => mockCacheManager.hasDataChanged(any(), any()))
        .thenAnswer((_) async => true);
    when(() => mockCacheManager.updateLastFetched(any()))
        .thenAnswer((_) async {});
    when(() => mockCacheManager.updateCacheMetadata(
          cacheKey: any(),
          recordCount: any(),
          policy: any(),
          dataHash: any(),
        )).thenAnswer((_) async {});
    when(() => mockCacheManager.generateDataHash(any()))
        .thenReturn('mock-hash');

    source = LocalAnnouncementsSource(db, mockCacheManager);
  });

  tearDown(() async {
    await db.close();
  });

  Announcement buildAnnouncement({int id = 1, String? title}) {
    return Announcement(
      id: id,
      title: title ?? 'Test Title $id',
      message: 'Test Message $id',
      priority: 'high',
      announcer: 'Admin',
      recipient: 'All',
      level: '100',
      programmeLevel: 'Science',
      createdAt: DateTime(2023, 1, id),
      updatedAt: DateTime(2023, 1, id),
    );
  }

  test('saveAnnouncements and getAnnouncements work', () async {
    final announcements = [
      buildAnnouncement(id: 1),
      buildAnnouncement(id: 2),
    ];

    await source.saveAnnouncements(announcements);

    final result = await source.getAnnouncements();
    expect(result.length, 2);
    expect(result[0].title, 'Test Title 1');
    expect(result[1].id, 2);
  });

  test('saveAnnouncements replaces old data', () async {
    await source.saveAnnouncements([buildAnnouncement(id: 1)]);
    await source.saveAnnouncements([buildAnnouncement(id: 2)]);
    final result = await source.getAnnouncements();
    expect(result.length, 1);
    expect(result[0].id, 2);
  });

  test('getAnnouncements returns empty list if none saved', () async {
    final result = await source.getAnnouncements();
    expect(result, isEmpty);
  });
}
