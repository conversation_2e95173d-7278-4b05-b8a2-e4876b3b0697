import 'package:flutter_test/flutter_test.dart';
import 'package:hooks_riverpod/hooks_riverpod.dart';
import 'package:mocktail/mocktail.dart';
import 'package:tertiary_mobile/features/courses/data/models/course_model.dart';
import 'package:tertiary_mobile/features/courses/data/models/course_specification_model.dart';
import 'package:tertiary_mobile/features/courses/data/repositories/courses_repository.dart';
import 'package:tertiary_mobile/features/courses/domain/course_table_item.dart';
import 'package:tertiary_mobile/features/courses/providers/course_registration_provider.dart';
import 'package:tertiary_mobile/shared/data/models/session_semester_model.dart';

class MockCoursesRepository extends Mock implements CoursesRepository {}

void main() {
  late ProviderContainer container;
  late MockCoursesRepository mockRepo;

  final course = Course(
    id: 1,
    courseCode: 'CSC101',
    courseTitle: 'Introduction to Computer Science',
    creditUnit: 3,
    courseSynopsis: 'Basic computer science concepts',
    type: 'Core',
    passMark: 40,
    lecturers: ['Dr<PERSON> <PERSON>', 'Prof<PERSON> <PERSON>'],
  );

  final session = Session(
    id: 1,
    sessionName: '2023/2024',
    semesters: [
      Semester(
        id: 1,
        title: 'First Semester',
        accronym: 'FIRST',
        position: 1,
        sessionId: 1,
      ),
    ],
  );

  final courseSpecification = CourseSpecification(
    id: 1,
    creditUnit: 3,
    units: '3',
    courseStatus: 'Active',
    courseStatusId: '1',
    semesterAccronym: 'FIRST',
    semesterTitle: 'First Semester',
    status: 1,
    passMarkRequired: 40,
    isSiwes: 0,
    hasPreRequisites: 0,
    isUsedBeyondQualifierLevel: 1,
    isUsedInResultComputation: 1,
    session: session,
    course: course,
  );

  setUp(() {
    mockRepo = MockCoursesRepository();
    container = ProviderContainer(
      overrides: [
        coursesRepositoryProvider.overrideWithValue(mockRepo),
      ],
    );
  });

  tearDown(() {
    container.dispose();
  });

  test('build returns cached course specifications immediately when available',
      () async {
    when(() => mockRepo.getCachedCourseSpecifications(1))
        .thenAnswer((_) async => [courseSpecification]);
    when(() => mockRepo.fetchCourseSpecifications(1))
        .thenAnswer((_) async => [courseSpecification]);

    final result =
        await container.read(courseRegistrationNotifierProvider(1).future);
    expect(result.specifications.length, 1);
    expect(result.specifications.first.courseCode, 'CSC101');
    expect(result.specifications.first.creditUnit, 3);
    verify(() => mockRepo.getCachedCourseSpecifications(1)).called(1);
  });

  test('build fetches from remote when no cached data', () async {
    when(() => mockRepo.getCachedCourseSpecifications(1))
        .thenAnswer((_) async => <CourseSpecification>[]);
    when(() => mockRepo.fetchCourseSpecifications(1))
        .thenAnswer((_) async => [courseSpecification]);

    final result =
        await container.read(courseRegistrationNotifierProvider(1).future);
    expect(result.specifications.length, 1);
    expect(result.specifications.first.courseCode, 'CSC101');
    verify(() => mockRepo.getCachedCourseSpecifications(1)).called(1);
    verify(() => mockRepo.fetchCourseSpecifications(1)).called(1);
  });

  test('refresh method works correctly for pull-to-refresh', () async {
    when(() => mockRepo.fetchCourseSpecifications(1))
        .thenAnswer((_) async => [courseSpecification]);

    final notifier =
        container.read(courseRegistrationNotifierProvider(1).notifier);

    // Call refresh method (simulating pull-to-refresh)
    await notifier.refresh(1);

    final state = container.read(courseRegistrationNotifierProvider(1));
    expect(state, isA<AsyncData<CourseRegistrationState>>());
    expect(state.value?.specifications.length, 1);
    expect(state.value?.specifications.first.courseCode, 'CSC101');
    // With background refresh, fetchCourseSpecifications is called twice
    verify(() => mockRepo.fetchCourseSpecifications(1)).called(2);
  });

  test('refresh preserves selected courses during refresh', () async {
    // Setup initial state with selected course
    when(() => mockRepo.getCachedCourseSpecifications(1))
        .thenAnswer((_) async => [courseSpecification]);
    when(() => mockRepo.fetchCourseSpecifications(1))
        .thenAnswer((_) async => [courseSpecification]);

    final notifier =
        container.read(courseRegistrationNotifierProvider(1).notifier);

    // Wait for initial build
    await container.read(courseRegistrationNotifierProvider(1).future);

    // Add a course to selection
    final tableItem = courseSpecification.toTableItem();
    notifier.addCourse(tableItem);

    // Verify course is selected
    var state = container.read(courseRegistrationNotifierProvider(1)).value!;
    expect(state.selected.length, 1);
    expect(state.selected.first.courseCode, 'CSC101');

    // Refresh and verify selected courses are preserved
    await notifier.refresh(1);

    state = container.read(courseRegistrationNotifierProvider(1)).value!;
    expect(state.selected.length, 1);
    expect(state.selected.first.courseCode, 'CSC101');
    expect(state.specifications.first.isRegistered, true);
  });

  test('addCourse marks course as registered', () async {
    when(() => mockRepo.getCachedCourseSpecifications(1))
        .thenAnswer((_) async => [courseSpecification]);
    when(() => mockRepo.fetchCourseSpecifications(1))
        .thenAnswer((_) async => [courseSpecification]);

    final notifier =
        container.read(courseRegistrationNotifierProvider(1).notifier);

    // Wait for initial build
    await container.read(courseRegistrationNotifierProvider(1).future);

    // Add course
    final tableItem = courseSpecification.toTableItem();
    notifier.addCourse(tableItem);

    final state = container.read(courseRegistrationNotifierProvider(1)).value!;
    expect(state.selected.length, 1);
    expect(state.selected.first.isRegistered, true);
    expect(state.specifications.first.isRegistered, true);
  });

  test('removeCourse unmarks course as registered', () async {
    when(() => mockRepo.getCachedCourseSpecifications(1))
        .thenAnswer((_) async => [courseSpecification]);
    when(() => mockRepo.fetchCourseSpecifications(1))
        .thenAnswer((_) async => [courseSpecification]);

    final notifier =
        container.read(courseRegistrationNotifierProvider(1).notifier);

    // Wait for initial build
    await container.read(courseRegistrationNotifierProvider(1).future);

    // Add course first
    final tableItem = courseSpecification.toTableItem();
    notifier.addCourse(tableItem);

    // Verify it's added
    var state = container.read(courseRegistrationNotifierProvider(1)).value!;
    expect(state.selected.length, 1);

    // Remove course
    notifier.removeCourse(tableItem);

    state = container.read(courseRegistrationNotifierProvider(1)).value!;
    expect(state.selected.length, 0);
    expect(state.specifications.first.isRegistered, false);
  });
}
